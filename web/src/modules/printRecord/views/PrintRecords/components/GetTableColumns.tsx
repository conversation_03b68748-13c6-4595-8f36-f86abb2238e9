/**
 * MineAdmin is committed to providing solutions for quickly building web applications
 * Please view the LICENSE file that was distributed with this source code,
 * For the full copyright and license information.
 * Thank you very much for using MineAdmin.
 *
 * <AUTHOR>
 * @Link   https://github.com/mineadmin
 */
import type { MaProTableColumns, MaProTableExpose } from '@mineadmin/pro-table'
import type { PrintRecordsVo } from '~/printRecord/api/PrintRecords.ts'
import type { UseDialogExpose } from '@/hooks/useDialog.ts'

import { useMessage } from '@/hooks/useMessage.ts'
import { deleteByIds } from '~/printRecord/api/PrintRecords.ts'
import { ResultCode } from '@/utils/ResultCode.ts'
import hasAuth from '@/utils/permission/hasAuth.ts'

export default function getTableColumns(dialog: UseDialogExpose, formRef: any, t: any): MaProTableColumns[] {
  const dictStore = useDictStore()
  const msg = useMessage()

  const showBtn = (auth: string | string[], row: PrintRecordsVo) => {
    return hasAuth(auth)
  }

  return [
    // 多选列
    { type: 'selection', showOverflowTooltip: false, label: () => t('crud.selection') },
    // 索引序号列
    { type: 'index' },
    // 普通列
                                                                                        { label: () =>  '快运母单号' , prop: 'parent_waybill_code' },
                        { label: () =>  '电子面单号' , prop: 'waybill_code' },
                        { label: () =>  '物流公司编码' , prop: 'wp_code' },
                        { label: () =>  '收货人省份' , prop: 'receiver_province' },
                        { label: () =>  '收货人城市' , prop: 'receiver_city' },
                        { label: () =>  '收货人地区' , prop: 'receiver_district' },
                        { label: () =>  '收货人街道' , prop: 'receiver_town' },
                        { label: () =>  '收货人名字' , prop: 'receiver_name' },
                        { label: () =>  '收货人手机' , prop: 'receiver_phone' },
                        { label: () =>  '收件人邮编' , prop: 'receiver_zip' },
                        { label: () =>  '收货地址' , prop: 'receiver_address' },
                        { label: () =>  '买家留言' , prop: 'buyer_remark' },
                        { label: () =>  '礼品网appId' , prop: 'app_id' },
                        { label: () =>  '打印数据' , prop: 'print_data' },
                        { label: () =>  '批次号' , prop: 'batch_no' },
                        { label: () =>  '姓名搜索索引' , prop: 'name_index' },
                        { label: () =>  '手机号搜索索引' , prop: 'phone_index' },
                        { label: () =>  '打印序号' , prop: 'print_index' },
                        { label: () =>  '打印总数' , prop: 'print_count' },
                        { label: () =>  '给哪个店铺打印' , prop: 'to_shop_id' },
                        { label: () =>  '外部订单号' , prop: 'outer_order_no' },
                        { label: () =>  '模板id' , prop: 'template_id' },
                        { label: () =>  '模板名' , prop: 'template_name' },
                        { label: () =>  '版本号' , prop: 'version' },
                        { label: () =>  '创建人' , prop: 'created_by' },
                        { label: () =>  '更新人' , prop: 'updated_by' },
                        { label: () =>  '订单类型 1普通订单 2自由打印 3代打订单' , prop: 'order_type' },
                        { label: () =>  '发货内容' , prop: 'send_content' },
                        { label: () =>  '网点ID' , prop: 'company_id' },
                        { label: () =>  '' , prop: 'created_at' },
                        { label: () =>  '' , prop: 'updated_at' },
                        { label: () =>  '' , prop: 'deleted_at' },
          
    // 操作列
    {
      type: 'operation',
      label: () => t('crud.operation'),
      width: '260px',
      operationConfigure: {
        type: 'tile',
        actions: [
          {
            name: 'edit',
            icon: 'i-heroicons:pencil',
            show: ({ row }) => showBtn('printRecord:print_records:update', row),
            text: () => t('crud.edit'),
            onClick: ({ row }) => {
              dialog.setTitle(t('crud.edit'))
              dialog.open({ formType: 'edit', data: row })
            },
          },
          {
            name: 'del',
            show: ({ row }) => showBtn('printRecord:print_records:delete', row),
            icon: 'i-heroicons:trash',
            text: () => t('crud.delete'),
            onClick: ({ row }, proxy: MaProTableExpose) => {
              msg.delConfirm(t('crud.delDataMessage')).then(async () => {
                const response = await deleteByIds([row.id])
                if (response.code === ResultCode.SUCCESS) {
                  msg.success(t('crud.delSuccess'))
                  await proxy.refresh()
                }
              })
            },
          },
        ],
      },
    },
  ]
}
