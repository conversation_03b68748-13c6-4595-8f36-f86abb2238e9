import type { ResponseStruct } from '#/global'

export interface PrintRecordsVo {
  // 
  id: number
  // 用户id
  user_id: string
  // 店铺id
  shop_id: string
  // 订单表id
  order_id: number
  // 取号记录表id
  history_id: number
  // 订单编号
  order_no: string
  // 包裹ID
  package_id: number
  // 快运母单号
  parent_waybill_code: string
  // 电子面单号
  waybill_code: string
  // 物流公司编码
  wp_code: string
  // 收货人省份
  receiver_province: string
  // 收货人城市
  receiver_city: string
  // 收货人地区
  receiver_district: string
  // 收货人街道
  receiver_town: string
  // 收货人名字
  receiver_name: string
  // 收货人手机
  receiver_phone: string
  // 收件人邮编
  receiver_zip: string
  // 收货地址
  receiver_address: string
  // 买家留言
  buyer_remark: string
  // 礼品网appId
  app_id: string
  // 打印数据
  print_data: string
  // 批次号
  batch_no: string
  // 姓名搜索索引
  name_index: string
  // 手机号搜索索引
  phone_index: string
  // 打印序号
  print_index: string
  // 打印总数
  print_count: string
  // 给哪个店铺打印
  to_shop_id: number
  // 外部订单号
  outer_order_no: string
  // 模板id
  template_id: string
  // 模板名
  template_name: string
  // 版本号
  version: string
  // 创建人
  created_by: string
  // 更新人
  updated_by: string
  // 订单类型 1普通订单 2自由打印 3代打订单
  order_type: string
  // 发货内容
  send_content: string
  // 网点ID
  company_id: number
  // 
  created_at: string
  // 
  updated_at: string
  // 
  deleted_at: string
}

// print_records查询
export function page(params: PrintRecordsVo): Promise<ResponseStruct<PrintRecordsVo[]>> {
return useHttp().get('/admin/print_record/print_records/list', { params })
}

// print_records新增
export function create(data: PrintRecordsVo): Promise<ResponseStruct<null>> {
  return useHttp().post('/admin/print_record/print_records', data)
}

// print_records编辑
export function save(id: number, data: PrintRecordsVo): Promise<ResponseStruct<null>> {
    return useHttp().put(`/admin/print_record/print_records/${id}`, data)
}

// print_records删除
export function deleteByIds(ids: number[]): Promise<ResponseStruct<null>> {
      return useHttp().delete('/admin/print_record/print_records', { data: ids })
}