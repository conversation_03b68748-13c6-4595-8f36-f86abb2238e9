name: fxerp

#volumes:
#  mine_redis_data:
#  mine_mysql_data:

services:
  hyperf:
    image: hyperf/hyperf:8.1-alpine-v3.18-swoole
    volumes:
      - ./:/www
    working_dir: /www
    ports:
      - "${DOCKER_HYPERF_HOST_PORT:-9501}:9501"
      - "${DOCKER_HYPERF_SWAGGER_PORT:-9503}:9503"
    environment:
      - TZ=Asia/Shanghai
      - APP_NAME=fxerp
    command:
      - sh
      - -c
      - |
        ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
        composer install -o
        php bin/hyperf.php start
