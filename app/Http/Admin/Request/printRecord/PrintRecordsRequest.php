<?php

namespace App\Http\Admin\Request\printRecord;

use Hyperf\Validation\Request\FormRequest;


class PrintRecordsRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    
        ];
    }

    public function attributes(): array
    {
        return ['id' => '','user_id' => '用户id','shop_id' => '店铺id','order_id' => '订单表id','history_id' => '取号记录表id','order_no' => '订单编号','package_id' => '包裹ID','parent_waybill_code' => '快运母单号','waybill_code' => '电子面单号','wp_code' => '物流公司编码','receiver_province' => '收货人省份','receiver_city' => '收货人城市','receiver_district' => '收货人地区','receiver_town' => '收货人街道','receiver_name' => '收货人名字','receiver_phone' => '收货人手机','receiver_zip' => '收件人邮编','receiver_address' => '收货地址','buyer_remark' => '买家留言','app_id' => '礼品网appId','print_data' => '打印数据','batch_no' => '批次号','name_index' => '姓名搜索索引','phone_index' => '手机号搜索索引','print_index' => '打印序号','print_count' => '打印总数','to_shop_id' => '给哪个店铺打印','outer_order_no' => '外部订单号','template_id' => '模板id','template_name' => '模板名','version' => '版本号','created_by' => '创建人','updated_by' => '更新人','order_type' => '订单类型 1普通订单 2自由打印 3代打订单','send_content' => '发货内容','company_id' => '网点ID','created_at' => '','updated_at' => '','deleted_at' => '',];
    }

}