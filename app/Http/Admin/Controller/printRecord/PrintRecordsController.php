<?php

namespace App\Http\Admin\Controller\printRecord;

use App\Service\printRecord\PrintRecordsService as Service;
use App\Http\Admin\Request\printRecord\PrintRecordsRequest as Request;
use Hyperf\Swagger\Annotation as OA;
use App\Http\Admin\Controller\AbstractController;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use App\Http\Admin\Middleware\PermissionMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use Mine\Access\Attribute\Permission;
use Hyperf\HttpServer\Annotation\Middleware;
use Mine\Swagger\Attributes\ResultResponse;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Delete;



#[OA\Tag('print_records')]
#[OA\HyperfServer('http')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
class PrintRecordsController extends AbstractController
{
    public function __construct(
        private readonly Service $service,
        private readonly CurrentUser $currentUser
    ) {}

    #[Get(
path: '/admin/print_record/print_records/list',
        operationId: 'printRecord:print_records:list',
        summary: 'print_records列表',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['print_records'],
    )]
    #[Permission(code: 'printRecord:print_records:list')]
    public function pageList(): Result
    {
        return $this->success(
            $this->service->page(
                $this->getRequestData(),
                $this->getCurrentPage(),
                $this->getPageSize()
            )
        );
    }

    #[Post(
path: '/admin/print_record/print_records',
        operationId: 'printRecord:print_records:create',
        summary: '新增print_records',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['print_records'],
    )]
    #[Permission(code: 'printRecord:print_records:create')]
    #[ResultResponse(instance: new Result())]
    public function create(Request $request): Result
    {
        $this->service->create(array_merge($request->validated(), [
            'created_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Put(
path: '/admin/print_record/print_records/{id}',
        operationId: 'printRecord:print_records:update',
        summary: '保存print_records',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['print_records'],
    )]
    #[Permission(code: 'printRecord:print_records:update')]
    #[ResultResponse(instance: new Result())]
    public function save(int $id, Request $request): Result
    {
        $this->service->updateById($id, array_merge($request->validated(), [
            'updated_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Delete(
path: '/admin/print_record/print_records',
        operationId: 'printRecord:print_records:delete',
        summary: '删除print_records',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['print_records'],
    )]
    #[ResultResponse(instance: new Result())]
    #[Permission(code: 'printRecord:print_records:delete')]
    public function delete(): Result
    {
        $this->service->deleteById($this->getRequestData());
        return $this->success();
    }

}
