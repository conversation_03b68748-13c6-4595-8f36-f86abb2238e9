<?php

namespace App\Model\printRecord;

use Hyperf\DbConnection\Model\Model as MineModel;


/**
* Class print_records
* @property string $id 
* @property string $user_id 
* @property string $shop_id 
* @property string $order_id 
* @property string $history_id 
* @property string $order_no 
* @property string $package_id 
* @property string $parent_waybill_code 
* @property string $waybill_code 
* @property string $wp_code 
* @property string $receiver_province 
* @property string $receiver_city 
* @property string $receiver_district 
* @property string $receiver_town 
* @property string $receiver_name 
* @property string $receiver_phone 
* @property string $receiver_zip 
* @property string $receiver_address 
* @property string $buyer_remark 
* @property string $app_id 
* @property string $print_data 
* @property string $batch_no 
* @property string $name_index 
* @property string $phone_index 
* @property string $print_index 
* @property string $print_count 
* @property string $to_shop_id 
* @property string $outer_order_no 
* @property string $template_id 
* @property string $template_name 
* @property string $version 
* @property string $created_by 
* @property string $updated_by 
* @property string $order_type 
* @property string $send_content 
* @property string $company_id 
* @property string $created_at 
* @property string $updated_at 
* @property string $deleted_at 
*/
class PrintRecords extends MineModel
{
    protected ?string $table = 'print_records';

    protected array $fillable = ['id','user_id','shop_id','order_id','history_id','order_no','package_id','parent_waybill_code','waybill_code','wp_code','receiver_province','receiver_city','receiver_district','receiver_town','receiver_name','receiver_phone','receiver_zip','receiver_address','buyer_remark','app_id','print_data','batch_no','name_index','phone_index','print_index','print_count','to_shop_id','outer_order_no','template_id','template_name','version','created_by','updated_by','order_type','send_content','company_id','created_at','updated_at','deleted_at',];

    protected array $casts = ['id' => 'string','user_id' => 'string','shop_id' => 'string','order_id' => 'string','history_id' => 'string','order_no' => 'string','package_id' => 'string','parent_waybill_code' => 'string','waybill_code' => 'string','wp_code' => 'string','receiver_province' => 'string','receiver_city' => 'string','receiver_district' => 'string','receiver_town' => 'string','receiver_name' => 'string','receiver_phone' => 'string','receiver_zip' => 'string','receiver_address' => 'string','buyer_remark' => 'string','app_id' => 'string','print_data' => 'string','batch_no' => 'string','name_index' => 'string','phone_index' => 'string','print_index' => 'string','print_count' => 'string','to_shop_id' => 'string','outer_order_no' => 'string','template_id' => 'string','template_name' => 'string','version' => 'string','created_by' => 'string','updated_by' => 'string','order_type' => 'string','send_content' => 'string','company_id' => 'string','created_at' => 'string','updated_at' => 'string','deleted_at' => 'string',];
}