<?php

namespace App\Repository\printRecord;

use App\Repository\IRepository;
use App\Model\printRecord\PrintRecords as Model;
use Hyperf\Database\Model\Builder;


class PrintRecordsRepository extends IRepository
{
    public function __construct(
        protected readonly Model $model
    ) {}

    public function handleSearch(Builder $query, array $params): Builder
    {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    
        return $query;
    }
}
